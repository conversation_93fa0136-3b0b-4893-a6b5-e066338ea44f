{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 0, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 0, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 0, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 0, "tid": 1, "ts": 1752466218001691, "dur": 5016738, "ph": "X", "name": "Build ScriptAssemblies", "args": {}}, {"pid": 0, "tid": 1, "ts": 1752466218003973, "dur": 110374, "ph": "X", "name": "WriteBuildProgramData", "args": {}}, {"pid": 0, "tid": 1, "ts": 1752466218115037, "dur": 231725, "ph": "X", "name": "<PERSON>", "args": {}}, {"pid": 0, "tid": 1, "ts": 1752466218346807, "dur": 2328087, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 0, "tid": 1, "ts": 1752466220674898, "dur": 2242407, "ph": "X", "name": "<PERSON>", "args": {}}, {"pid": 0, "tid": 1, "ts": 1752466222917337, "dur": 101091, "ph": "X", "name": "Finish", "args": {}}, {"pid": 0, "tid": 1, "ts": 1752466223021404, "dur": 2332, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 0, "tid": 1, "ts": 1752466223032450, "dur": 1549, "ph": "X", "name": "", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752466218194651, "dur": 45419, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218240087, "dur": 2043, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "RemoveStaleOutputs"}}, {"pid": 12345, "tid": 0, "ts": 1752466218242230, "dur": 83, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1752466218242316, "dur": 1285, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "BuildQueueInit"}}, {"pid": 12345, "tid": 0, "ts": 1752466218243641, "dur": 3102, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218246743, "dur": 7, "ph": "X", "name": "SortWorkingStack", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218249108, "dur": 954, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250062, "dur": 71, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250133, "dur": 202, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250336, "dur": 159, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250496, "dur": 7, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250503, "dur": 20, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250523, "dur": 3, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250526, "dur": 113, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250639, "dur": 15, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250654, "dur": 25, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250679, "dur": 6, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250685, "dur": 15, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250700, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250705, "dur": 13, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250718, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250723, "dur": 15, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250738, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250744, "dur": 12, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250756, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250761, "dur": 14, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250775, "dur": 6, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250781, "dur": 13, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250794, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250799, "dur": 15, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250814, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250819, "dur": 13, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250832, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250837, "dur": 12, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250850, "dur": 4, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250855, "dur": 12, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250867, "dur": 4, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250871, "dur": 13, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250884, "dur": 6, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250890, "dur": 12, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250902, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250907, "dur": 13, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250920, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250925, "dur": 11, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250936, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250941, "dur": 12, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250953, "dur": 6, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250959, "dur": 12, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250971, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250976, "dur": 18, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250994, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218250999, "dur": 11, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218251010, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218251015, "dur": 13, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218251028, "dur": 0, "ph": "X", "name": "SharedResourceDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218251029, "dur": 49, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218251163, "dur": 44297, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752466218243566, "dur": 3243, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466218246814, "dur": 1779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466218248593, "dur": 1384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466218245177, "dur": 2100, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466218247281, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466218248538, "dur": 1612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466218243545, "dur": 3215, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466218246768, "dur": 2323, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466218244014, "dur": 2977, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466218246994, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466218248112, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466218243974, "dur": 2986, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466218246963, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466218247849, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466218248751, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466218243798, "dur": 3104, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466218246907, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466218248250, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466218243937, "dur": 3011, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466218246954, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466218248149, "dur": 1358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466218244104, "dur": 2932, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466218247042, "dur": 1553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466218248595, "dur": 1729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466218244224, "dur": 2845, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466218247073, "dur": 1634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466218248708, "dur": 1470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466218244330, "dur": 2759, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466218247095, "dur": 1418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466218248514, "dur": 1765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466218244387, "dur": 2719, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466218247110, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466218248367, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466218244459, "dur": 2670, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466218247134, "dur": 1576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466218248710, "dur": 1532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466218244529, "dur": 2611, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466218247143, "dur": 1666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466218248810, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466218244578, "dur": 2571, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466218247153, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466218248402, "dur": 1469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466218244684, "dur": 2478, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466218247166, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466218248315, "dur": 1468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466218244730, "dur": 2445, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466218247179, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466218248427, "dur": 1358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466218245015, "dur": 2224, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466218247241, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466218248280, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466218244799, "dur": 2384, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466218247187, "dur": 1479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466218248667, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466218244911, "dur": 2293, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466218247210, "dur": 1721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466218248931, "dur": 1303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466218244975, "dur": 2252, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466218247233, "dur": 1636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466218248870, "dur": 1638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466218245113, "dur": 2147, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466218247265, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466218248420, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466218243703, "dur": 3152, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466218246861, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466218248513, "dur": 1499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466218245225, "dur": 2071, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466218247300, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466218248519, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466218245356, "dur": 1955, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466218247312, "dur": 1392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466218248704, "dur": 1502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466218296780, "dur": 678, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 0, "tid": 1, "ts": 1752466223034297, "dur": 956, "ph": "X", "name": "backend_profiler0.traceevents", "args": {}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1752466220162051, "dur": 434449, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752466220163767, "dur": 117030, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752466220508112, "dur": 18310, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752466220526426, "dur": 70058, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752466220527668, "dur": 39594, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752466220603154, "dur": 1760, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752466220601957, "dur": 2991, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"pid": 0, "tid": 1, "ts": 1752466223035339, "dur": 194, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752466220778448, "dur": 1189, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466220779644, "dur": 1436, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "RemoveStaleOutputs"}}, {"pid": 12345, "tid": 0, "ts": 1752466220781120, "dur": 77, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1752466220781199, "dur": 1106, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "BuildQueueInit"}}, {"pid": 12345, "tid": 0, "ts": 1752466220782339, "dur": 2311, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466220784650, "dur": 6, "ph": "X", "name": "SortWorkingStack", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222793004, "dur": 630, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222793634, "dur": 376, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794010, "dur": 575, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794585, "dur": 20, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794605, "dur": 6, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794612, "dur": 28, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794640, "dur": 6, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794646, "dur": 13, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794659, "dur": 6, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794665, "dur": 17, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794682, "dur": 66, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794748, "dur": 13, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794761, "dur": 7, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794768, "dur": 16, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794784, "dur": 9, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794793, "dur": 14, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794807, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794812, "dur": 14, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794826, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794832, "dur": 13, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794845, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794850, "dur": 15, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794865, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794870, "dur": 18, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794888, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794893, "dur": 12, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794905, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794910, "dur": 12, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794922, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794927, "dur": 12, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794939, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794944, "dur": 13, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794957, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794962, "dur": 13, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794975, "dur": 4, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222794980, "dur": 25, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222795005, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222795010, "dur": 12, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222795022, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222795027, "dur": 13, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222795040, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222795045, "dur": 13, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222795058, "dur": 4, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222795062, "dur": 14, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222795076, "dur": 5, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222795081, "dur": 12, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222795093, "dur": 4, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222795098, "dur": 16, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222795114, "dur": 1, "ph": "X", "name": "SharedResourceDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222795136, "dur": 120, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222795281, "dur": 63499, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752466220782459, "dur": 2296, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220784760, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220786069, "dur": 1686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220787755, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220788932, "dur": 1239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220790172, "dur": 2181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220792353, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220792563, "dur": 20, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Poly2Tri.AdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1752466220792584, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220792647, "dur": 2, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.AdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1752466220792649, "dur": 46, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220792697, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752466220792893, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220792967, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_2088AA34236C480F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752466220793039, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220793097, "dur": 25, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_98B8D0908142736A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752466220793123, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220793211, "dur": 19, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_6B7BAEBD7231D585.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752466220793230, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220793350, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E71EE13415D7C04F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752466220793406, "dur": 47, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220793456, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Toolchain.Win-x86_64-Linux-x86_64.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752466220793663, "dur": 45, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220793710, "dur": 33, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752466220793743, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220793798, "dur": 46, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752466220793844, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220793913, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220794497, "dur": 7, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752466220794504, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220794563, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220794818, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220795513, "dur": 1789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220797303, "dur": 1435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220798738, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220798817, "dur": 20, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220798838, "dur": 19, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_16F8EA6B67022FB5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752466220798857, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220799003, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220799075, "dur": 13, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.SysrootPackage.Editor.ref.dll_08B16BA041802A06.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752466220799088, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220799209, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220799772, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220800118, "dur": 11, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752466220800130, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220800196, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220800318, "dur": 1181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752466220801500, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220801582, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220802021, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220802103, "dur": 47, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220802150, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220802406, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220802610, "dur": 7, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752466220802618, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220802681, "dur": 1297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220803978, "dur": 1152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220805131, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220805197, "dur": 1131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466220806329, "dur": 682097, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752466221488427, "dur": 1304550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220782709, "dur": 2185, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220784899, "dur": 1443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220786343, "dur": 1649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220787992, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220789382, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220790554, "dur": 3381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220793935, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220794507, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220794848, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220795474, "dur": 27, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 2, "ts": 1752466220795502, "dur": 1196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220796701, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220797324, "dur": 1429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220798754, "dur": 46, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220798801, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220798863, "dur": 8, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220798872, "dur": 47, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ProBuilder.Poly2Tri.ref.dll_CBA2A2F4F1308037.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752466220798920, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220799009, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220799081, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220799152, "dur": 636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220799788, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220800172, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220800300, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Stl.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752466220800641, "dur": 40, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220800683, "dur": 892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752466220801576, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220801633, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220802037, "dur": 8, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Cinemachine.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1752466220802045, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220802114, "dur": 15, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ProBuilder.ref.dll_8918F62D623CB1E2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752466220802129, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220802187, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752466220802513, "dur": 41, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220802635, "dur": 1361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220803997, "dur": 1195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220805193, "dur": 1138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466220806332, "dur": 682093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752466221488425, "dur": 1304572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220782978, "dur": 1942, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220784926, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220786117, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220787491, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220788648, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220789976, "dur": 2994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220792971, "dur": 18, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_E358491571284AA1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752466220792990, "dur": 31, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220793023, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_58BD5AD560E812A8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752466220793032, "dur": 26, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220793059, "dur": 12, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_3BFEA7B8ECAE198E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752466220793071, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220793218, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIServiceModule.dll_9409805676853F93.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752466220793390, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220793465, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752466220793689, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220793866, "dur": 620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220794487, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_7423F2FBC6CF80C4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752466220794497, "dur": 35, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220794539, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752466220794753, "dur": 27, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220794841, "dur": 659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220795501, "dur": 1761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220797264, "dur": 1513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Poly2Tri.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752466220798778, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220798870, "dur": 2214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752466220801084, "dur": 985, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220802113, "dur": 1654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752466220803767, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220803962, "dur": 976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752466220804938, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220805125, "dur": 1006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752466220806131, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220806287, "dur": 10417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752466220817386, "dur": 136, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466220817531, "dur": 653638, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752466221488384, "dur": 1679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752466221491317, "dur": 212, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466221491545, "dur": 599393, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752466222104857, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752466222105107, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752466222105114, "dur": 1023, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752466222106143, "dur": 686851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220782344, "dur": 2344, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220784691, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220785825, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220786642, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220787880, "dur": 941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220788822, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220789617, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220790421, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220791115, "dur": 1421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220792553, "dur": 49, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1752466220792602, "dur": 36, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220792645, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752466220792744, "dur": 33, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220792778, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Stl.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1752466220792779, "dur": 21, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220792801, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_3FAD722AB6509F7E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752466220793092, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220793148, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_8F7E70238170CD14.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752466220793157, "dur": 33, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220793192, "dur": 11, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_70DA857FC0AA791D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752466220793203, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220793285, "dur": 42, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_068EC1D71A957645.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752466220793328, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220793396, "dur": 2, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Sysroot.Linux_x86_64.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1752466220793398, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220793461, "dur": 2, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Toolchain.Win-x86_64-Linux-x86_64.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1752466220793463, "dur": 39, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220793507, "dur": 26, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VSCode.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1752466220793533, "dur": 44, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220793581, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752466220793787, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220793855, "dur": 33, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752466220793889, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220793965, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752466220794215, "dur": 31, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220794248, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220794505, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220794815, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220795471, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752466220795474, "dur": 33, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220795517, "dur": 1793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220797311, "dur": 1445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220798756, "dur": 42, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220798799, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.KdTree.dll"}}, {"pid": 12345, "tid": 4, "ts": 1752466220798804, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220799205, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220799803, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220800149, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220800302, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752466220800668, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220800729, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1752466220800733, "dur": 41, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220800776, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752466220801113, "dur": 34, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220801149, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.SysrootPackage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752466220801484, "dur": 34, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220801519, "dur": 27, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220801547, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220802018, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220802100, "dur": 46, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220802146, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220802385, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220802618, "dur": 1332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220803950, "dur": 1183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220805133, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220805184, "dur": 1117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466220806301, "dur": 682136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752466221488437, "dur": 1304573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220784664, "dur": 0, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220784676, "dur": 7851, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220792560, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.AdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1752466220792615, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220792673, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.SysrootPackage.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752466220792846, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220792922, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_F5418AA8DA294385.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752466220793122, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220793208, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_65312608BB2FDC23.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752466220793217, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220793307, "dur": 10, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_73E22CC3752521B1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752466220793317, "dur": 46, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220793365, "dur": 28, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_397582C31B3530CF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752466220793393, "dur": 34, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220793429, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Cinemachine.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752466220793574, "dur": 29, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220793604, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752466220793730, "dur": 38, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220793770, "dur": 19, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752466220793789, "dur": 27, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220793817, "dur": 21, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Toolchain.Win-x86_64-Linux-x86_64.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752466220793839, "dur": 48, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220793888, "dur": 609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220794497, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220794823, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220795469, "dur": 10, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_A805A84D9B0C22D9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752466220795479, "dur": 35, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220795523, "dur": 1252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752466220796775, "dur": 26, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220796802, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220797290, "dur": 2764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752466220800055, "dur": 629, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220800688, "dur": 26, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VSCode.Editor.ref.dll_AE868214B5ED3D13.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752466220800714, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220800773, "dur": 1172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752466220801945, "dur": 46, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220802026, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220802129, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1752466220802134, "dur": 48, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220802188, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220802415, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220802629, "dur": 1353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220803982, "dur": 1223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220805205, "dur": 1122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466220806327, "dur": 682108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752466221488435, "dur": 1304598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220782556, "dur": 2245, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220784806, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220786190, "dur": 1769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220787960, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220789137, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220790293, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220791334, "dur": 1210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220792555, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Poly2Tri.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752466220792755, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220792817, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_57B48561A7B69BF9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752466220793112, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220793195, "dur": 36, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_0773B571E950BD79.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752466220793231, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220793309, "dur": 35, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_D7C2E27A9AC75801.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752466220793344, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220793432, "dur": 12, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Cinemachine.AdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1752466220793445, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220793524, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/GoogleMobileAds.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752466220793729, "dur": 46, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220793777, "dur": 38, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/GoogleMobileAds.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752466220793815, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220793881, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220794517, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220794834, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220795481, "dur": 1810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220797293, "dur": 2115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/GoogleMobileAds.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752466220799408, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220799643, "dur": 7, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/GoogleMobileAds.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752466220799650, "dur": 40, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220799692, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220799764, "dur": 15, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 6, "ts": 1752466220799780, "dur": 1731, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220801513, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752466220801522, "dur": 45, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220801569, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220802027, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220802097, "dur": 8, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752466220802105, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220802179, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220802417, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220802632, "dur": 1311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220803944, "dur": 1173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220805118, "dur": 48, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220805167, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752466220805401, "dur": 31, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220805433, "dur": 853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220806287, "dur": 1092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752466220807379, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220807474, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor-firstpass.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752466220807479, "dur": 25, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466220807506, "dur": 680885, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752466221488392, "dur": 1304595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220782682, "dur": 2165, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220784853, "dur": 1382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220786236, "dur": 1598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220787835, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220788996, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220789753, "dur": 2055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220791809, "dur": 732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220792559, "dur": 15, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.AdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1752466220792575, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220792643, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.KdTree.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752466220792817, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220792874, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_664048C53F6A56E2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752466220793080, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220793156, "dur": 19, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_9ED2520879D331F9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752466220793175, "dur": 49, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220793226, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E6E83D3572106D4D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752466220793290, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220793354, "dur": 26, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_16954B87F3FD0194.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752466220793380, "dur": 43, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220793426, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752466220793837, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220793915, "dur": 603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220794519, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220794812, "dur": 7, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752466220794819, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220794881, "dur": 626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220795508, "dur": 1818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220797327, "dur": 1387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220798715, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.Stl.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752466220798720, "dur": 40, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220798762, "dur": 35, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220798798, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.KdTree.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752466220798802, "dur": 34, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220798840, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752466220798843, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220798913, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220799084, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220799135, "dur": 48, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.Csg.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752466220799183, "dur": 693, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220799878, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220800155, "dur": 1163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220801318, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220801554, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220802015, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220802098, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220802155, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220802388, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220802620, "dur": 1317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220803938, "dur": 1165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220805103, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220805195, "dur": 1147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466220806342, "dur": 682067, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752466221488409, "dur": 1304564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220782792, "dur": 2113, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220784907, "dur": 941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220785848, "dur": 1784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220787633, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220788489, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220789335, "dur": 1628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220790964, "dur": 2242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220793209, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_5DAFDD1F009C23E5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752466220793297, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220793445, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752466220793817, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220793886, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220794500, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220794820, "dur": 673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220795493, "dur": 1790, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220797285, "dur": 2648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752466220799934, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220800107, "dur": 20, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_DA88E6A3E9EB6155.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752466220800127, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220800189, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220800308, "dur": 881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/GoogleMobileAds.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752466220801190, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220801253, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220801579, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220802132, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220802409, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220802622, "dur": 1309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220803931, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1752466220803935, "dur": 30, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220803974, "dur": 1146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220805120, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220805187, "dur": 1101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220806290, "dur": 7, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-firstpass.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1752466220806298, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466220806369, "dur": 682046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752466221488415, "dur": 1304590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220783119, "dur": 1815, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220784937, "dur": 1492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220786430, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220787993, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220788845, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220790186, "dur": 2009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220792196, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220792710, "dur": 18, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.AdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1752466220792729, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220792785, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Csg.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752466220793120, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220793189, "dur": 38, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_CCFBB8149C17C1F4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752466220793227, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220793280, "dur": 35, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PackageManagerUIModule.dll_A7112D5BDA3CABCD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752466220793315, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220793374, "dur": 18, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_0830DF3165063CD8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752466220793392, "dur": 45, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220793440, "dur": 24, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_3AA447436B90A0B1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752466220793464, "dur": 43, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220793518, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/com.unity.cinemachine.editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752466220793795, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220794046, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220794510, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220794852, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220795484, "dur": 1783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220797268, "dur": 1254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.SysrootPackage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752466220798522, "dur": 534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220799075, "dur": 1231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Sysroot.Linux_x86_64.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752466220800307, "dur": 1217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220801530, "dur": 765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Toolchain.Win-x86_64-Linux-x86_64.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752466220802296, "dur": 47, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220802390, "dur": 8, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Toolchain.Win-x86_64-Linux-x86_64.ref.dll_64DDAC33FF976D62.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752466220802398, "dur": 31, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220802431, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220802643, "dur": 1328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220803972, "dur": 1154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220805127, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220805198, "dur": 1115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466220806313, "dur": 682138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752466221488452, "dur": 1304569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220783244, "dur": 1714, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220784965, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220786288, "dur": 1502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220787790, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220789002, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220790124, "dur": 3011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220793136, "dur": 39, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F5EC262C9C4B0BFF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752466220793175, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220793269, "dur": 38, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_38D6432671BBADE8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752466220793308, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220793367, "dur": 40, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_5D6A290AC330BD46.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752466220793407, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220793505, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752466220793713, "dur": 48, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220793763, "dur": 34, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752466220793797, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220793852, "dur": 34, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752466220793886, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220793961, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220794515, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220794865, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220795526, "dur": 1772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220797299, "dur": 1425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220798725, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220798804, "dur": 36, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220798840, "dur": 35, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220798876, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.Poly2Tri.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1752466220798885, "dur": 2500, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220801388, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220801500, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752466220801504, "dur": 34, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220801539, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220802030, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220802136, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220802384, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Toolchain.Win-x86_64-Linux-x86_64.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1752466220802389, "dur": 42, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220802432, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220802616, "dur": 1319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220803935, "dur": 1173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220805109, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220805179, "dur": 1124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466220806303, "dur": 682083, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466221488387, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1752466221488731, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752466221488760, "dur": 1950, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1752466221490714, "dur": 1302300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220783302, "dur": 1674, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220784980, "dur": 1575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220786556, "dur": 1698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220788254, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220789565, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220790795, "dur": 2227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220793024, "dur": 31, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_E57AE57E85D96271.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752466220793056, "dur": 34, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220793092, "dur": 10, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_CB9337F9716D360F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752466220793102, "dur": 30, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220793133, "dur": 12, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_74741C04357F22D3.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752466220793145, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220793416, "dur": 19, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_C6F7A6426394BAE3.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752466220793435, "dur": 47, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220793494, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1752466220793497, "dur": 49, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220793548, "dur": 18, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_0E1F4B9B5D397BAD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752466220793566, "dur": 43, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220793630, "dur": 2, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1752466220793632, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220793734, "dur": 38, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752466220793773, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220793841, "dur": 29, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Sysroot.Linux_x86_64.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752466220793870, "dur": 46, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220793917, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220794523, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220794861, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220795495, "dur": 1774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220797270, "dur": 2612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752466220799883, "dur": 1072, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220800960, "dur": 29, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ProBuilder.AssetIdRemapUtility.ref.dll_975A82D5AEE9C802.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752466220800989, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220801083, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752466220801600, "dur": 35, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220801637, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220802029, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220802104, "dur": 37, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220802141, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220802404, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220802606, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752466220803223, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220803280, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1752466220803283, "dur": 17, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220803301, "dur": 2, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1752466220803303, "dur": 12, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220803316, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220803941, "dur": 1262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220805203, "dur": 1119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466220806322, "dur": 682152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752466221488474, "dur": 1304529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220783388, "dur": 1598, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220784989, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220786405, "dur": 1624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220788029, "dur": 1685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220789714, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220790518, "dur": 2185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220792706, "dur": 11, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.AdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1752466220792717, "dur": 41, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220792759, "dur": 6, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1752466220792766, "dur": 29, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220792796, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_7BE25A94A639A992.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752466220792952, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220793009, "dur": 22, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_20D4E2CC9EF2F583.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752466220793032, "dur": 49, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220793084, "dur": 38, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_200E01610A294D82.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752466220793122, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220793315, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_322E8DEDD9262133.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752466220793397, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220793468, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752466220793590, "dur": 27, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220793625, "dur": 49, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752466220793675, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220793756, "dur": 17, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Stl.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752466220793773, "dur": 26, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220793800, "dur": 22, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752466220793822, "dur": 38, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220793861, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220794493, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220794825, "dur": 712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220795538, "dur": 1739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220797279, "dur": 26, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_0174E068A5FA251B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752466220797305, "dur": 2961, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220800297, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Sysroot.Linux_x86_64.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752466220800730, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220800798, "dur": 864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752466220801663, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220801726, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220802036, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220802140, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220802410, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220802612, "dur": 691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220803304, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220803934, "dur": 7, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1752466220803941, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220804022, "dur": 1090, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220805113, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220805169, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752466220805345, "dur": 23, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220805369, "dur": 991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466220806361, "dur": 682101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752466221488463, "dur": 1304528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220783478, "dur": 1527, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220785011, "dur": 1746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220786758, "dur": 1673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220788432, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220789692, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220790835, "dur": 1667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220792555, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752466220792715, "dur": 34, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220792750, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752466220792836, "dur": 24, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220792862, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsNativeModule.dll_F18AE1F14BB51BEF.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752466220792939, "dur": 42, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220792983, "dur": 11, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_89A92B50447A0229.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752466220792995, "dur": 39, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220793035, "dur": 11, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E0D78EA6F589B349.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752466220793046, "dur": 29, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220793077, "dur": 10, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_BC75C81D8DCDFD9B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752466220793087, "dur": 28, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220793117, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_D4AC1BFFE9BA1758.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752466220793126, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220793252, "dur": 11, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_3D4C34575788312D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752466220793263, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220793316, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_23E0145DB49D4419.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752466220793325, "dur": 31, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220793358, "dur": 15, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_57EBCBB1E1B7B268.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752466220793373, "dur": 24, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220793401, "dur": 8, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.AdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1752466220793409, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220793466, "dur": 16, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1752466220793482, "dur": 49, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220793533, "dur": 23, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_1617966024639C7A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752466220793556, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220793614, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752466220793834, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220793893, "dur": 673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220794566, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220794878, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220795515, "dur": 1780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220797296, "dur": 10, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1752466220797307, "dur": 3555, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220800866, "dur": 1198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752466220802064, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220802123, "dur": 6, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1752466220802129, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220802181, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220802390, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220802623, "dur": 1307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220803930, "dur": 8, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ProBuilder.Editor.ref.dll_638CF0286F7AC9EF.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752466220803938, "dur": 27, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220803995, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752466220804128, "dur": 24, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220804153, "dur": 969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220805122, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220805181, "dur": 1113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466220806295, "dur": 682093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466221488389, "dur": 616475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466222104865, "dur": 687096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1752466222791971, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752466222791978, "dur": 924, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1752466222792907, "dur": 2, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ScriptAssemblies"}}, {"pid": 12345, "tid": 13, "ts": 1752466222792909, "dur": 0, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220783594, "dur": 1434, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220785034, "dur": 1494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220786529, "dur": 1702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220788231, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220789426, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220790555, "dur": 3095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220793651, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752466220793716, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220793772, "dur": 35, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752466220793807, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220793860, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220794504, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220794817, "dur": 659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220795476, "dur": 1797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220797274, "dur": 2257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752466220799532, "dur": 548, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220800099, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_8A1461A5C565B4EA.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752466220800108, "dur": 33, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220800142, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220800346, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.KdTree.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752466220800783, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220801173, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220801532, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Sysroot.Linux_x86_64.ref.dll_0CF00D8B82A87F25.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752466220801541, "dur": 34, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220801578, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Toolchain.Win-x86_64-Linux-x86_64.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752466220801787, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220802209, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220802423, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220802639, "dur": 1314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220803954, "dur": 1217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220805194, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752466220805681, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220805747, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220806292, "dur": 1187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220807479, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor-firstpass.dll"}}, {"pid": 12345, "tid": 14, "ts": 1752466220807482, "dur": 27, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466220807510, "dur": 680956, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752466221488466, "dur": 1304541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220783701, "dur": 1349, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220785055, "dur": 1402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220786458, "dur": 1681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220788140, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220789306, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220790429, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220791429, "dur": 1102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220792553, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752466220792686, "dur": 31, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220792723, "dur": 1915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752466220794639, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220794813, "dur": 14, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_BDDAB6C3AC1C4D86.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752466220794827, "dur": 47, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220794906, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220795498, "dur": 1767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220797266, "dur": 1032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752466220798298, "dur": 525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220798836, "dur": 2525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752466220801361, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220801499, "dur": 8, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_17F688653313AA4C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752466220801507, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220801585, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752466220801763, "dur": 30, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220801794, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220802031, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220802106, "dur": 41, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220802147, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220802413, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220802615, "dur": 949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220803565, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/com.unity.cinemachine.editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1752466220803568, "dur": 26, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220803595, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220804016, "dur": 1094, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220805111, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220805177, "dur": 1122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466220806300, "dur": 682083, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466221488387, "dur": 295918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1752466221784316, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752466221784324, "dur": 209577, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1752466221993908, "dur": 799104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220783786, "dur": 1278, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220785067, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220786291, "dur": 1738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220788030, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220788935, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220789698, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220790475, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220791728, "dur": 805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220792556, "dur": 11, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.AdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1752466220792573, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220792644, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.KdTree.AdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1752466220792653, "dur": 32, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220792686, "dur": 14, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.SysrootPackage.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1752466220792701, "dur": 26, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220792728, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Stl.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752466220792823, "dur": 29, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220792853, "dur": 23, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_59237B4C5CE32991.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752466220792876, "dur": 44, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220792924, "dur": 13, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_0EBA57893D10E266.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752466220792937, "dur": 32, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220792970, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_56452327C26C627C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752466220792980, "dur": 29, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220793011, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_78AAE2B3A2E0628E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752466220793020, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220793257, "dur": 8, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_F563FDE5C804780F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752466220793265, "dur": 26, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220793295, "dur": 8, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_387B01732E130C2E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752466220793303, "dur": 33, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220793338, "dur": 8, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_ABF5A1D1FE9DAF09.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752466220793346, "dur": 25, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220793375, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Sysroot.Linux_x86_64.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752466220793449, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220793506, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752466220793598, "dur": 25, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220793626, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752466220793678, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220793760, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Poly2Tri.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752466220793769, "dur": 27, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220793799, "dur": 17, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752466220793816, "dur": 41, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220793858, "dur": 13, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.KdTree.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752466220793871, "dur": 33, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220793905, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220794530, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220794931, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220795479, "dur": 1791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220797272, "dur": 1297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Stl.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752466220798569, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220798714, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ProBuilder.Stl.ref.dll_22478948AA59BB5F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752466220798723, "dur": 23, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220798747, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220798847, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220798905, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220799099, "dur": 43, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220799142, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220799650, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/GoogleMobileAds.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1752466220799655, "dur": 34, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220799690, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220799765, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1752466220799770, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220799826, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220800104, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1752466220800109, "dur": 36, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220800146, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220800304, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Poly2Tri.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752466220800658, "dur": 44, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220800703, "dur": 13, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1752466220800716, "dur": 23, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220800741, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752466220801110, "dur": 26, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220801137, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752466220801482, "dur": 47, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220801533, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Sysroot.Linux_x86_64.dll"}}, {"pid": 12345, "tid": 16, "ts": 1752466220801538, "dur": 36, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220801575, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220802038, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220802124, "dur": 6, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220802131, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1752466220802135, "dur": 35, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220802171, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220802392, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220802608, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1752466220802612, "dur": 40, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220802656, "dur": 1304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220803961, "dur": 1178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220805139, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220805221, "dur": 1123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466220806344, "dur": 682142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752466221488486, "dur": 1304582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220783930, "dur": 1140, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220785075, "dur": 1582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220786658, "dur": 1590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220788249, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220789509, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220790758, "dur": 2572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220793331, "dur": 18, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_0D51DB3EAE610B45.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1752466220793350, "dur": 25, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220793376, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1752466220793452, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220793534, "dur": 10, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_89EB8C387FA834C8.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1752466220793544, "dur": 30, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220793580, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.AdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1752466220793581, "dur": 33, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220793636, "dur": 29, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1752466220793665, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220793743, "dur": 17, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1752466220793760, "dur": 40, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220793801, "dur": 18, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1752466220793819, "dur": 29, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220793850, "dur": 13, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1752466220793863, "dur": 44, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220793910, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220794542, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220794916, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220795474, "dur": 1798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220797273, "dur": 1382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Csg.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1752466220798656, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220799128, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ProBuilder.Csg.ref.dll_B9DF2CFD7269D35B.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1752466220799226, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220799284, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220799762, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_02929A3ECA791895.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1752466220799916, "dur": 48, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220799969, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220800100, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 17, "ts": 1752466220800104, "dur": 35, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220800140, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220800663, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1752466220801155, "dur": 36, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220801193, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220801532, "dur": 12, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Sysroot.Linux_x86_64.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1752466220801544, "dur": 25, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220801571, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220802012, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Cinemachine.dll"}}, {"pid": 12345, "tid": 17, "ts": 1752466220802017, "dur": 35, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220802125, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.dll"}}, {"pid": 12345, "tid": 17, "ts": 1752466220802129, "dur": 38, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220802169, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220802425, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220802649, "dur": 1298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220803948, "dur": 1145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220805095, "dur": 10, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ProBuilder.AddOns.Editor.ref.dll_ABC72966D7CAF3CC.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1752466220805105, "dur": 35, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220805175, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1752466220805429, "dur": 30, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220805460, "dur": 865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466220806325, "dur": 682146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1752466221488471, "dur": 1304547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220784019, "dur": 1075, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220785099, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220786547, "dur": 2191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220788739, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220789917, "dur": 2927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220792845, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_EBAF31984DD12490.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1752466220792942, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220793028, "dur": 20, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_34721BFC1438C833.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1752466220793049, "dur": 49, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220793101, "dur": 21, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_A93A24E686DC0CFE.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1752466220793123, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220793214, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_8E82D685ABB1A8E7.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1752466220793326, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220793391, "dur": 29, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_278E5A4EE37713FF.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1752466220793420, "dur": 47, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220793470, "dur": 2, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1752466220793472, "dur": 39, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220793519, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/com.unity.cinemachine.editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1752466220793521, "dur": 39, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220793571, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1752466220795551, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220796855, "dur": 685, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1752466220797542, "dur": 1175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220798718, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.Stl.dll"}}, {"pid": 12345, "tid": 18, "ts": 1752466220798727, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220798815, "dur": 23, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220798839, "dur": 8, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 18, "ts": 1752466220798847, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220799006, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220799079, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.SysrootPackage.Editor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1752466220799088, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220799233, "dur": 602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220799835, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220800157, "dur": 1160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220801317, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220801560, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220802017, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220802097, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220802158, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220802383, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Toolchain.Win-x86_64-Linux-x86_64.dll"}}, {"pid": 12345, "tid": 18, "ts": 1752466220802388, "dur": 40, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220802429, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220802627, "dur": 1360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220803988, "dur": 1211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220805199, "dur": 1110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466220806310, "dur": 682131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1752466221488441, "dur": 1304540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220784155, "dur": 958, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220785117, "dur": 1548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220786666, "dur": 2682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220789349, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220790499, "dur": 2795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220793295, "dur": 30, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_630D5BDDF7DA51E0.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1752466220793325, "dur": 30, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220793357, "dur": 36, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_866DF5065A45AC64.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1752466220793393, "dur": 28, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220793422, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1752466220793423, "dur": 46, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220793471, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.AddOns.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1752466220793589, "dur": 31, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220793626, "dur": 42, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1752466220793669, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220793875, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220794512, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220794838, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220795510, "dur": 1777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220797288, "dur": 2960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1752466220800249, "dur": 1874, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220802128, "dur": 17, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_0FE027DDD6F41767.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1752466220802145, "dur": 39, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220802192, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220802420, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220802630, "dur": 1373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220804003, "dur": 1102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220805106, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220805183, "dur": 1122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466220806306, "dur": 682138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1752466221488444, "dur": 1304556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220784268, "dur": 855, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220785125, "dur": 1495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220786621, "dur": 1625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220788247, "dur": 1452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220789700, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220790975, "dur": 1895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220792872, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E796AA7D74548698.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1752466220792926, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220792980, "dur": 17, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_66543F998FCD0037.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1752466220792998, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220793067, "dur": 20, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_21BA2DF095EF4280.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1752466220793087, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220793153, "dur": 17, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_2000FFDFB31605F9.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1752466220793170, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220793224, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_A1A77318693567F0.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1752466220793300, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220793448, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1752466220793451, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220793520, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/GoogleMobileAds.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1752466220793521, "dur": 28, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220793550, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.AdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1752466220793551, "dur": 31, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220793583, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.AdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1752466220793584, "dur": 27, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220793614, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1752466220793616, "dur": 21, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220793638, "dur": 16, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1752466220793654, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220793728, "dur": 24, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.SysrootPackage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1752466220793752, "dur": 34, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220793787, "dur": 14, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1752466220793802, "dur": 34, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220793838, "dur": 14, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1752466220793852, "dur": 36, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220793890, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220794489, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1752466220794493, "dur": 34, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220794528, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220794856, "dur": 667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220795524, "dur": 1776, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220797301, "dur": 1418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220798719, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220798810, "dur": 26, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220798837, "dur": 2969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1752466220801807, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220802013, "dur": 1447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1752466220803461, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220803558, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/com.unity.cinemachine.editor.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1752466220803562, "dur": 27, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220803591, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220803964, "dur": 1150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220805115, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220805239, "dur": 1095, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466220806335, "dur": 682083, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1752466221488418, "dur": 1304593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220784381, "dur": 751, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220785135, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220786324, "dur": 1658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220787983, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220789212, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220790377, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220791503, "dur": 1212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220792717, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1752466220792885, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220792954, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_73C37938DB0236DA.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1752466220793006, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220793088, "dur": 13, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_4C6C15EDF4B56EA6.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1752466220793101, "dur": 48, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220793151, "dur": 8, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_A423B371EC34031A.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1752466220793159, "dur": 28, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220793190, "dur": 11, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_970300161D2A8CDC.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1752466220793202, "dur": 28, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220793232, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_5B176EF38D4DEF79.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1752466220793287, "dur": 32, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220793321, "dur": 11, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_DEB464D687D7FC67.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1752466220793332, "dur": 28, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220793362, "dur": 15, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_E64B23A9F4AEB1D7.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1752466220793378, "dur": 30, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220793409, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1752466220793410, "dur": 30, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220793441, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_7FE6444F5B764644.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1752466220793497, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220793570, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1752466220793746, "dur": 46, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220793794, "dur": 45, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1752466220793839, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220793910, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1752466220794164, "dur": 48, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220794213, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220794540, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220794859, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220795550, "dur": 1747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220797298, "dur": 8, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 21, "ts": 1752466220797307, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220797398, "dur": 1323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220798722, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220798802, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220798961, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220799139, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.Csg.dll"}}, {"pid": 12345, "tid": 21, "ts": 1752466220799197, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220799295, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220799768, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220800111, "dur": 12, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 21, "ts": 1752466220800123, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220800186, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220800298, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Csg.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1752466220800947, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220801018, "dur": 7, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.AssetIdRemapUtility.pdb"}}, {"pid": 12345, "tid": 21, "ts": 1752466220801026, "dur": 41, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220801069, "dur": 7, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.AssetIdRemapUtility.dll"}}, {"pid": 12345, "tid": 21, "ts": 1752466220801076, "dur": 37, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220801115, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1752466220801504, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220801797, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220802034, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220802156, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220802408, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220802654, "dur": 1346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220804000, "dur": 1127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220805128, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220805189, "dur": 1123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466220806312, "dur": 682134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1752466221488447, "dur": 1304595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220784454, "dur": 696, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220785156, "dur": 2177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220787333, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220788597, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220789678, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220790511, "dur": 2581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220793094, "dur": 38, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_D7504D3036E29DC1.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1752466220793132, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220793206, "dur": 24, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D5143DFFF4F24AEB.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1752466220793230, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220793293, "dur": 23, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_ED5CB90754A27914.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1752466220793316, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220793400, "dur": 19, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_825FBE41DD63F9F9.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1752466220793420, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220793475, "dur": 2, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.AddOns.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1752466220793477, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220793546, "dur": 18, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_68BA1E4F67955F64.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1752466220793564, "dur": 47, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220793635, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Cinemachine.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1752466220793716, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220793769, "dur": 34, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Csg.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1752466220793803, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220793857, "dur": 26, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1752466220793884, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220793946, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220794521, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220794832, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220795503, "dur": 1777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220797282, "dur": 4668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1752466220801951, "dur": 584, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220802636, "dur": 20, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_A558B9E65539D7D0.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1752466220802656, "dur": 43, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220802704, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1752466220802976, "dur": 24, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220803002, "dur": 1000, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220804002, "dur": 1094, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220805098, "dur": 12, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.AddOns.Editor.dll"}}, {"pid": 12345, "tid": 22, "ts": 1752466220805111, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220805201, "dur": 1158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466220806359, "dur": 682037, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1752466221488397, "dur": 1304574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220784591, "dur": 580, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220785171, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220786301, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220787454, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220788283, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220789105, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220789919, "dur": 2600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220792558, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1752466220792832, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220792929, "dur": 1462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1752466220794391, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220794488, "dur": 885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1752466220795373, "dur": 46, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220795468, "dur": 1702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1752466220797170, "dur": 49, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220797263, "dur": 1426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.KdTree.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1752466220798689, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220798796, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ProBuilder.KdTree.ref.dll_CD6FCED2DD3D2D29.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1752466220798805, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220798878, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.Poly2Tri.dll"}}, {"pid": 12345, "tid": 23, "ts": 1752466220798883, "dur": 27, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220798911, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220799076, "dur": 6, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.SysrootPackage.Editor.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1752466220799083, "dur": 2936, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220802023, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220802094, "dur": 8, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 23, "ts": 1752466220802103, "dur": 564, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220802669, "dur": 1339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220804008, "dur": 1166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220805175, "dur": 1115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220806295, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-firstpass.dll"}}, {"pid": 12345, "tid": 23, "ts": 1752466220806300, "dur": 41, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466220806346, "dur": 682056, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1752466221488402, "dur": 1304583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220782431, "dur": 2306, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220784743, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220786059, "dur": 1690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220787750, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220788834, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220789615, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220790423, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220791448, "dur": 1090, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220792557, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1752466220792721, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220792791, "dur": 2, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Csg.AdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1752466220792793, "dur": 42, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220792837, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UNETModule.dll_29A947FF6A398255.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1752466220792891, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220792949, "dur": 18, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_BBABAFAA6A85EFF8.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1752466220792967, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220793039, "dur": 16, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_D1188B3F132CB906.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1752466220793055, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220793121, "dur": 26, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_565CFC05D025666D.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1752466220793147, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220793203, "dur": 17, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_1722A2225DB982AE.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1752466220793221, "dur": 46, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220793271, "dur": 17, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_C38A828086EA0887.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1752466220793288, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220793346, "dur": 29, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_95E409EA833C94C2.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1752466220793375, "dur": 39, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220793419, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ProBuilder.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1752466220793759, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220793869, "dur": 626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220794496, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220794813, "dur": 6, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 24, "ts": 1752466220794820, "dur": 29, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220794850, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220795505, "dur": 1770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220797293, "dur": 2373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1752466220799667, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220799760, "dur": 2154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1752466220801914, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220802092, "dur": 16, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_D0BA3615D322EF6C.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1752466220802108, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220802162, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220802400, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220802626, "dur": 1320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220803946, "dur": 1153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220805105, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.AddOns.Editor.pdb"}}, {"pid": 12345, "tid": 24, "ts": 1752466220805114, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220805319, "dur": 977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466220806297, "dur": 682097, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1752466221488394, "dur": 1304589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752466222860368, "dur": 8569, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 0, "tid": 1, "ts": 1752466223035600, "dur": 8409, "ph": "X", "name": "backend_profiler1.traceevents", "args": {}}, {"pid": 0, "tid": 1, "ts": 1752466223027891, "dur": 16156, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}