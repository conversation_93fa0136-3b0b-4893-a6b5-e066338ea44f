using UnityEngine;

/// <summary>
/// Handles collision detection for individual walker prefabs
/// This component should be attached to each walker prefab
/// </summary>
public class WalkerCollisionHandler : MonoBehaviour
{
    [Header("Collision Settings")]
    public string playerTag = "Player"; // Tag to identify player vehicle
    public LayerMask playerLayerMask = -1; // Layer mask for player detection
    
    private WaypointWalker waypointWalker;
    private int walkerIndex = -1;
    private bool isDead = false;

    /// <summary>
    /// Initialize the collision handler with reference to the waypoint walker
    /// </summary>
    public void Initialize(WaypointWalker walker, int index)
    {
        waypointWalker = walker;
        walkerIndex = index;
        isDead = false;

        Debug.Log($"✅ Walker {walkerIndex} collision handler initialized - Ready for trigger detection!");
    }

    /// <summary>
    /// Handle trigger collision with player vehicle (primary detection method)
    /// </summary>

    private void OnTriggerEnter(Collider other)
    {
        // Skip if already dead
        if (isDead) return;

        Debug.Log($"🎯 Walker {walkerIndex} (GameObject: {gameObject.name}) trigger detected with: {other.gameObject.name}");

        // Check if collision is with player vehicle
        if (IsPlayerVehicle(other.gameObject))
        {
            // Trigger death animation through the waypoint walker
            if (waypointWalker != null && walkerIndex >= 0)
            {
                Debug.Log($"💀 TRIGGERING DEATH for Walker {walkerIndex} (GameObject: {gameObject.name}) hit by: {other.gameObject.name}");
                waypointWalker.TriggerWalkerDeath(walkerIndex);
                isDead = true;

                Debug.Log($"✅ WALKER {walkerIndex} DEATH COMMAND SENT - IMMEDIATE DEATH!");
            }
        }
        else
        {
            Debug.Log($"⚠️ Walker {walkerIndex} triggered by non-player object: {other.gameObject.name}");
        }
    }

    /// <summary>
    /// Check if the given GameObject is a player vehicle
    /// </summary>
    private bool IsPlayerVehicle(GameObject obj)
    {
        // Check by tag first
        if (!string.IsNullOrEmpty(playerTag) && obj.CompareTag(playerTag))
        {
            return true;
        }
        
        // Check if it's the active player vehicle from RCC
        if (RCC_SceneManager.Instance != null && RCC_SceneManager.Instance.activePlayerVehicle != null)
        {
            // Check if this object or its parent is the player vehicle
            Transform checkTransform = obj.transform;
            while (checkTransform != null)
            {
                if (checkTransform.gameObject == RCC_SceneManager.Instance.activePlayerVehicle.gameObject)
                {
                    return true;
                }
                checkTransform = checkTransform.parent;
            }
        }
        
        // Check by layer mask
        if (((1 << obj.layer) & playerLayerMask) != 0)
        {
            return true;
        }
        
        // Check for RCC_CarControllerV3 component
        if (obj.GetComponent<RCC_CarControllerV3>() != null || obj.GetComponentInParent<RCC_CarControllerV3>() != null)
        {
            return true;
        }
        
        return false;
    }

    /// <summary>
    /// Mark this walker as dead (called externally)
    /// </summary>
    public void SetDead(bool dead)
    {
        isDead = dead;
    }

    /// <summary>
    /// Check if this walker is dead
    /// </summary>
    public bool IsDead()
    {
        return isDead;
    }
}
