using UnityEngine;

/// <summary>
/// Handles collision detection for individual walker prefabs
/// This component should be attached to each walker prefab
/// </summary>
public class WalkerCollisionHandler : MonoBehaviour
{
    [Header("Collision Settings")]
    public string playerTag = "Player"; // Tag to identify player vehicle
    public LayerMask playerLayerMask = -1; // Layer mask for player detection
    
    private WaypointWalker waypointWalker;
    private int walkerIndex = -1;
    private bool isDead = false;
    private bool isProcessingDeath = false; // Prevent multiple death triggers

    /// <summary>
    /// Initialize the collision handler with reference to the waypoint walker
    /// </summary>
    public void Initialize(WaypointWalker walker, int index)
    {
        waypointWalker = walker;
        walkerIndex = index;
        isDead = false;
        isProcessingDeath = false; // Reset processing flag

        Debug.Log($"✅ Walker {walkerIndex} collision handler initialized!");
    }

    /// <summary>
    /// Handle trigger collision with player vehicle (primary detection method)
    /// </summary>

    private void OnTriggerEnter(Collider other)
    {
        // Skip if already dead or processing death
        if (isDead || isProcessingDeath)
        {
            Debug.Log($"⚠️ Walker {walkerIndex} trigger ignored - isDead: {isDead}, isProcessingDeath: {isProcessingDeath}");
            return;
        }

        Debug.Log($"🎯 TRIGGER: Walker {walkerIndex} (GameObject: {gameObject.name}) detected collision with: {other.gameObject.name}");
        Debug.Log($"🔍 This collision handler is on GameObject: {gameObject.name} at position: {transform.position}");

        // Check if collision is with player vehicle
        if (IsPlayerVehicle(other.gameObject))
        {
            // Simple trigger - immediate death on contact
            if (waypointWalker != null && walkerIndex >= 0)
            {
                isProcessingDeath = true; // Prevent multiple triggers

                Debug.Log($"💀 DEATH TRIGGER: Walker {walkerIndex} (THIS GameObject: {gameObject.name}) being killed by: {other.gameObject.name}");
                Debug.Log($"🎯 This collision handler belongs to: {gameObject.name} with index: {walkerIndex}");
                Debug.Log($"🎯 Calling TriggerWalkerDeath for index: {walkerIndex}");

                // Double check - verify this collision handler is on the correct GameObject
                if (waypointWalker.spawnedPrefabs != null && walkerIndex < waypointWalker.spawnedPrefabs.Count)
                {
                    GameObject expectedWalker = waypointWalker.spawnedPrefabs[walkerIndex];
                    if (expectedWalker == gameObject)
                    {
                        Debug.Log($"✅ VERIFIED: Collision handler is on correct walker {walkerIndex}");
                    }
                    else
                    {
                        Debug.LogError($"❌ MISMATCH: Collision handler on {gameObject.name} but expected on {expectedWalker.name}");
                        isProcessingDeath = false; // Reset flag on error
                        return;
                    }
                }

                waypointWalker.TriggerWalkerDeath(walkerIndex);
                isDead = true;

                Debug.Log($"✅ Death command sent for Walker {walkerIndex} (GameObject: {gameObject.name})");
            }
            else
            {
                Debug.LogError($"❌ Cannot trigger death - waypointWalker: {waypointWalker}, walkerIndex: {walkerIndex}");
                isProcessingDeath = false; // Reset flag on error
            }
        }
        else
        {
            Debug.Log($"⚠️ Non-player object triggered Walker {walkerIndex}: {other.gameObject.name}");
        }
    }

    /// <summary>
    /// Check if the given GameObject is a player vehicle
    /// </summary>
    private bool IsPlayerVehicle(GameObject obj)
    {
        // Check by tag first
        if (!string.IsNullOrEmpty(playerTag) && obj.CompareTag(playerTag))
        {
            return true;
        }
        
        // Check if it's the active player vehicle from RCC
        if (RCC_SceneManager.Instance != null && RCC_SceneManager.Instance.activePlayerVehicle != null)
        {
            // Check if this object or its parent is the player vehicle
            Transform checkTransform = obj.transform;
            while (checkTransform != null)
            {
                if (checkTransform.gameObject == RCC_SceneManager.Instance.activePlayerVehicle.gameObject)
                {
                    return true;
                }
                checkTransform = checkTransform.parent;
            }
        }
        
        // Check by layer mask
        if (((1 << obj.layer) & playerLayerMask) != 0)
        {
            return true;
        }
        
        // Check for RCC_CarControllerV3 component
        if (obj.GetComponent<RCC_CarControllerV3>() != null || obj.GetComponentInParent<RCC_CarControllerV3>() != null)
        {
            return true;
        }
        
        return false;
    }

    /// <summary>
    /// Mark this walker as dead (called externally)
    /// </summary>
    public void SetDead(bool dead)
    {
        isDead = dead;
    }

    /// <summary>
    /// Check if this walker is dead
    /// </summary>
    public bool IsDead()
    {
        return isDead;
    }

    /// <summary>
    /// Reset walker to alive state (for testing/debugging)
    /// </summary>
    public void ResetToAlive()
    {
        isDead = false;
        isProcessingDeath = false;
        Debug.Log($"🔄 Walker {walkerIndex} reset to alive state");
    }

    /// <summary>
    /// Get current processing state
    /// </summary>
    public bool IsProcessingDeath()
    {
        return isProcessingDeath;
    }
}
