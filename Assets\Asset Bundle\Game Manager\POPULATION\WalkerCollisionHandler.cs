using UnityEngine;

/// <summary>
/// Handles collision detection for individual walker prefabs
/// This component should be attached to each walker prefab
/// </summary>
public class WalkerCollisionHandler : MonoBehaviour
{
    [Header("Collision Settings")]
    public string playerTag = "Player"; // Tag to identify player vehicle
    public LayerMask playerLayerMask = -1; // Layer mask for player detection
    
    private WaypointWalker waypointWalker;
    private int walkerIndex = -1;
    private bool isDead = false;

    /// <summary>
    /// Initialize the collision handler with reference to the waypoint walker
    /// </summary>
    public void Initialize(WaypointWalker walker, int index)
    {
        waypointWalker = walker;
        walkerIndex = index;
        isDead = false;
    }

    /// <summary>
    /// Handle collision with player vehicle
    /// </summary>
    private void OnCollisionEnter(Collision collision)
    {
        // Skip if already dead
        if (isDead) return;
        
        // Check if collision is with player vehicle
        if (IsPlayerVehicle(collision.gameObject))
        {
            // Trigger death animation through the waypoint walker
            if (waypointWalker != null && walkerIndex >= 0)
            {
                waypointWalker.TriggerWalkerDeath(walkerIndex);
                isDead = true;
                
                Debug.Log($"Walker {walkerIndex} hit by player vehicle: {collision.gameObject.name}");
            }
        }
    }

    /// <summary>
    /// Alternative trigger-based collision detection
    /// </summary>
    private void OnTriggerEnter(Collider other)
    {
        // Skip if already dead
        if (isDead) return;
        
        // Check if collision is with player vehicle
        if (IsPlayerVehicle(other.gameObject))
        {
            // Trigger death animation through the waypoint walker
            if (waypointWalker != null && walkerIndex >= 0)
            {
                waypointWalker.TriggerWalkerDeath(walkerIndex);
                isDead = true;
                
                Debug.Log($"Walker {walkerIndex} triggered by player vehicle: {other.gameObject.name}");
            }
        }
    }

    /// <summary>
    /// Check if the given GameObject is a player vehicle
    /// </summary>
    private bool IsPlayerVehicle(GameObject obj)
    {
        // Check by tag first
        if (!string.IsNullOrEmpty(playerTag) && obj.CompareTag(playerTag))
        {
            return true;
        }
        
        // Check if it's the active player vehicle from RCC
        if (RCC_SceneManager.Instance != null && RCC_SceneManager.Instance.activePlayerVehicle != null)
        {
            // Check if this object or its parent is the player vehicle
            Transform checkTransform = obj.transform;
            while (checkTransform != null)
            {
                if (checkTransform.gameObject == RCC_SceneManager.Instance.activePlayerVehicle.gameObject)
                {
                    return true;
                }
                checkTransform = checkTransform.parent;
            }
        }
        
        // Check by layer mask
        if (((1 << obj.layer) & playerLayerMask) != 0)
        {
            return true;
        }
        
        // Check for RCC_CarControllerV3 component
        if (obj.GetComponent<RCC_CarControllerV3>() != null || obj.GetComponentInParent<RCC_CarControllerV3>() != null)
        {
            return true;
        }
        
        return false;
    }

    /// <summary>
    /// Mark this walker as dead (called externally)
    /// </summary>
    public void SetDead(bool dead)
    {
        isDead = dead;
    }

    /// <summary>
    /// Check if this walker is dead
    /// </summary>
    public bool IsDead()
    {
        return isDead;
    }
}
