using UnityEngine;

/// <summary>
/// Simple test script to verify walker death system is working correctly
/// Attach this to any GameObject in the scene to monitor walker deaths
/// </summary>
public class WalkerDeathTester : MonoBehaviour
{
    [Header("Testing")]
    public bool enableDetailedLogging = true;
    public KeyCode testKey = KeyCode.T;
    
    private WaypointWalker waypointWalker;
    
    void Start()
    {
        // Find the waypoint walker in the scene
        waypointWalker = FindObjectOfType<WaypointWalker>();
        
        if (waypointWalker != null)
        {
            Debug.Log("✅ WalkerDeathTester: Found WaypointWalker system");
        }
        else
        {
            Debug.LogError("❌ WalkerDeathTester: No WaypointWalker found in scene!");
        }
    }
    
    void Update()
    {
        // Press T key to show current walker status
        if (Input.GetKeyDown(testKey))
        {
            ShowWalkerStatus();
        }
    }
    
    void ShowWalkerStatus()
    {
        if (waypointWalker == null) return;
        
        Debug.Log("=== WALKER STATUS ===");
        Debug.Log($"Total Walkers: {waypointWalker.spawnedPrefabs.Count}");
        
        for (int i = 0; i < waypointWalker.spawnedPrefabs.Count; i++)
        {
            GameObject walker = waypointWalker.spawnedPrefabs[i];
            if (walker != null)
            {
                WalkerCollisionHandler handler = walker.GetComponent<WalkerCollisionHandler>();
                bool isDead = handler != null ? handler.IsDead() : false;
                
                Debug.Log($"Walker {i}: {walker.name} - Position: {walker.transform.position} - Dead: {isDead}");
            }
            else
            {
                Debug.Log($"Walker {i}: NULL");
            }
        }
        Debug.Log("===================");
    }
    
    // Called when any walker dies (you can call this from WaypointWalker if needed)
    public void OnWalkerDied(int walkerIndex, string walkerName)
    {
        if (enableDetailedLogging)
        {
            Debug.Log($"🔥 WALKER DEATH CONFIRMED: Walker {walkerIndex} ({walkerName}) has died!");
        }
    }
}
