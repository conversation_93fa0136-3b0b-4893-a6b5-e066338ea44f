using UnityEngine;

/// <summary>
/// Simple test script to verify walker death system is working correctly
/// Attach this to any GameObject in the scene to monitor walker deaths
/// </summary>
public class WalkerDeathTester : MonoBehaviour
{
    [Header("Testing")]
    public bool enableDetailedLogging = true;
    public KeyCode testKey = KeyCode.T;
    
    private WaypointWalker waypointWalker;
    
    void Start()
    {
        // Find the waypoint walker in the scene
        waypointWalker = FindObjectOfType<WaypointWalker>();
        
        if (waypointWalker != null)
        {
            Debug.Log("✅ WalkerDeathTester: Found WaypointWalker system");
        }
        else
        {
            Debug.LogError("❌ WalkerDeathTester: No WaypointWalker found in scene!");
        }
    }
    
    void Update()
    {
        // Press T key to show current walker status
        if (Input.GetKeyDown(testKey))
        {
            ShowWalkerStatus();
        }
    }
    
    void ShowWalkerStatus()
    {
        if (waypointWalker == null) return;

        Debug.Log("=== DETAILED WALKER STATUS ===");
        Debug.Log($"Total Walkers: {waypointWalker.spawnedPrefabs.Count}");
        Debug.Log($"Death Animation Parameter: '{waypointWalker.deathAnimationParameter}'");

        for (int i = 0; i < waypointWalker.spawnedPrefabs.Count; i++)
        {
            GameObject walker = waypointWalker.spawnedPrefabs[i];
            if (walker != null)
            {
                WalkerCollisionHandler handler = walker.GetComponent<WalkerCollisionHandler>();
                Animator animator = walker.GetComponent<Animator>();

                bool isDead = handler != null ? handler.IsDead() : false;
                bool hasAnimator = animator != null;
                bool hasController = hasAnimator && animator.runtimeAnimatorController != null;
                bool isActive = walker.activeInHierarchy;

                Debug.Log($"Walker {i}: {walker.name}");
                Debug.Log($"  - Position: {walker.transform.position}");
                Debug.Log($"  - Active: {isActive}");
                Debug.Log($"  - Dead: {isDead}");
                Debug.Log($"  - Has Animator: {hasAnimator}");
                Debug.Log($"  - Has Controller: {hasController}");

                if (hasAnimator && hasController)
                {
                    Debug.Log($"  - Controller: {animator.runtimeAnimatorController.name}");
                    Debug.Log($"  - Animator Enabled: {animator.enabled}");

                    // Check for death parameter
                    bool hasDeathParam = false;
                    foreach (AnimatorControllerParameter param in animator.parameters)
                    {
                        if (param.name == waypointWalker.deathAnimationParameter)
                        {
                            hasDeathParam = true;
                            Debug.Log($"  - Death Parameter Found: {param.name} ({param.type})");
                            break;
                        }
                    }
                    if (!hasDeathParam)
                    {
                        Debug.LogError($"  - ❌ Death Parameter '{waypointWalker.deathAnimationParameter}' NOT FOUND!");
                    }
                }
            }
            else
            {
                Debug.Log($"Walker {i}: NULL");
            }
        }
        Debug.Log("===============================");
    }
    
    // Called when any walker dies (you can call this from WaypointWalker if needed)
    public void OnWalkerDied(int walkerIndex, string walkerName)
    {
        if (enableDetailedLogging)
        {
            Debug.Log($"🔥 WALKER DEATH CONFIRMED: Walker {walkerIndex} ({walkerName}) has died!");
        }
    }
}
