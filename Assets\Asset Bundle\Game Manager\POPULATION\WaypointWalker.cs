using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class WaypointWalker : MonoBehaviour
{
    [Header("Waypoint Settings")]
    public WPC waypointsContainer; // Reference to your WPC component
    public GameObject[] prefabToSpawn; // Assign your prefabs in the Inspector
    public float moveSpeed = 2f; // Speed of movement
    public int numberOfClones = 5; // Number of clones to generate

    [Header("Player Detection")]
    public Transform player; // Assign the player's transform in the Inspector
    public float activationRange = 50f; // Range within which prefabs should be active
    public float playerStopRadius = 10f; // Distance at which walkers stop when player is near

    [Header("Animation Settings")]
    public string walkAnimationParameter = "isWalking"; // Bool parameter for walking animation
    public string idleAnimationParameter = "isIdle"; // Bool parameter for idle animation
    public string deathAnimationParameter = "isDead"; // Trigger parameter for death animation

    // Private lists for managing spawned prefabs
    private List<GameObject> spawnedPrefabs = new List<GameObject>();
    private List<int> currentWaypointIndices = new List<int>();
    private List<int> waypointDirections = new List<int>(); // 1 for forward, -1 for backward
    private List<Animator> spawnedAnimators = new List<Animator>(); // Animators for each spawned prefab
    private List<bool> walkerDeathStates = new List<bool>(); // Track death state for each walker
    private static GameObject parentObject; // Static parent object for organizing all prefabs

    private void Start()
    {
        if (waypointsContainer.waypoints.Count == 0)
        {
            Debug.LogWarning("No waypoints found. Please assign waypoints in the inspector.");
            return;
        }

        if (prefabToSpawn.Length == 0)
        {
            Debug.LogWarning("No prefabs assigned. Please assign prefabs in the inspector.");
            return;
        }

        // Create the parent object if it doesn't exist yet
        if (parentObject == null)
        {
            parentObject = new GameObject("SpawnedPrefabsParent");
        }

        int waypointsCount = waypointsContainer.waypoints.Count;
        int prefabsCount = prefabToSpawn.Length;
        int clonesToSpawn = Mathf.Min(numberOfClones, prefabsCount * waypointsCount);

        for (int i = 0; i < clonesToSpawn; i++)
        {
            int prefabIndex = i % prefabsCount; // Cycle through prefabs
            int waypointIndex = i % waypointsCount; // Cycle through waypoints
            SpawnPrefabAtWaypoint(prefabIndex, waypointIndex);
            currentWaypointIndices.Add(waypointIndex);
            waypointDirections.Add(1); // Start by moving forward
            walkerDeathStates.Add(false); // Initialize as alive
        }
    }

    private void SpawnPrefabAtWaypoint(int prefabIndex, int waypointIndex)
    {
        Vector3 spawnPosition = waypointsContainer.waypoints[waypointIndex].transform.position;
        GameObject spawnedPrefab = Instantiate(prefabToSpawn[prefabIndex], spawnPosition, Quaternion.identity);

        // Set the prefab as a child of the single parent object
        spawnedPrefab.transform.parent = parentObject.transform;

        // Get the Animator component from the spawned prefab
        Animator animator = spawnedPrefab.GetComponent<Animator>();
        if (animator == null)
        {
            // Try to get animator from children if not found on root
            animator = spawnedPrefab.GetComponentInChildren<Animator>();
        }

        // Add trigger collider for collision detection
        if (spawnedPrefab.GetComponent<Collider>() == null)
        {
            // Add a capsule trigger collider for precise collision detection
            CapsuleCollider triggerCollider = spawnedPrefab.AddComponent<CapsuleCollider>();
            triggerCollider.height = 1.6f; // Human height
            triggerCollider.radius = 0.25f; // Smaller radius for precise hit detection
            triggerCollider.center = new Vector3(0, 0.8f, 0); // Center at waist level
            triggerCollider.isTrigger = true; // Use trigger for immediate detection
        }
        else
        {
            // Make sure existing collider is set as trigger and adjust size
            Collider existingCollider = spawnedPrefab.GetComponent<Collider>();
            existingCollider.isTrigger = true;

            // If it's a capsule collider, adjust the size for precise detection
            if (existingCollider is CapsuleCollider capsule)
            {
                capsule.radius = 0.25f; // Smaller radius for precise hit detection
                capsule.height = 1.6f;
                capsule.center = new Vector3(0, 0.8f, 0);
            }
        }

        // Add rigidbody if not present (needed for collision detection)
        if (spawnedPrefab.GetComponent<Rigidbody>() == null)
        {
            Rigidbody rb = spawnedPrefab.AddComponent<Rigidbody>();
            rb.isKinematic = true; // Kinematic so it doesn't fall due to gravity
            rb.useGravity = false;
        }

        // Add to lists first to get correct index
        spawnedPrefabs.Add(spawnedPrefab);
        spawnedAnimators.Add(animator); // Add animator to list (can be null if not found)

        // Get the correct walker index (current count - 1 since we just added it)
        int walkerIndex = spawnedPrefabs.Count - 1;

        // Add the collision handler component for immediate trigger detection
        WalkerCollisionHandler collisionHandler = spawnedPrefab.GetComponent<WalkerCollisionHandler>();
        if (collisionHandler == null)
        {
            collisionHandler = spawnedPrefab.AddComponent<WalkerCollisionHandler>();
        }

        // Initialize the collision handler with the correct walker index
        collisionHandler.Initialize(this, walkerIndex);

        Debug.Log($"🚶 Walker {walkerIndex} spawned with TRIGGER collision detection at {spawnedPrefab.transform.position}");
    }

    /// <summary>
    /// Gets the current player position from RCC Scene Manager or fallback to assigned player transform
    /// </summary>
    private Vector3 GetPlayerPosition()
    {
        // Try to get player position from RCC Scene Manager first
        if (RCC_SceneManager.Instance != null && RCC_SceneManager.Instance.activePlayerVehicle != null)
        {
            return RCC_SceneManager.Instance.activePlayerVehicle.transform.position;
        }

        // Fallback to manually assigned player transform
        if (player != null)
        {
            return player.position;
        }

        // Return zero if no player found
        return Vector3.zero;
    }

    /// <summary>
    /// Checks if player is within the stop radius of a walker
    /// </summary>
    private bool IsPlayerNearWalker(Vector3 walkerPosition)
    {
        Vector3 playerPos = GetPlayerPosition();
        if (playerPos == Vector3.zero) return false;

        float distance = Vector3.Distance(playerPos, walkerPosition);
        return distance <= playerStopRadius;
    }

    /// <summary>
    /// Checks if player vehicle exists and is active
    /// </summary>
    private bool IsPlayerVehicleActive()
    {
        return (RCC_SceneManager.Instance != null && RCC_SceneManager.Instance.activePlayerVehicle != null) || player != null;
    }

    /// <summary>
    /// Sets the walking animation state for a walker
    /// </summary>
    private void SetWalkingAnimation(int walkerIndex, bool isWalking)
    {
        if (walkerIndex < 0 || walkerIndex >= spawnedAnimators.Count) return;

        Animator animator = spawnedAnimators[walkerIndex];
        if (animator == null) return;

        // Set walking state
        if (!string.IsNullOrEmpty(walkAnimationParameter))
        {
            animator.SetBool(walkAnimationParameter, isWalking);
        }

        // Set idle state (opposite of walking)
        if (!string.IsNullOrEmpty(idleAnimationParameter))
        {
            animator.SetBool(idleAnimationParameter, !isWalking);
        }
    }

    /// <summary>
    /// Triggers the death animation for a walker (called by collision handler)
    /// </summary>
    public void TriggerWalkerDeath(int walkerIndex)
    {
        TriggerDeathAnimation(walkerIndex);
    }

    /// <summary>
    /// Internal method to trigger the death animation for a walker
    /// </summary>
    private void TriggerDeathAnimation(int walkerIndex)
    {
        Debug.Log($"🎯 TriggerDeathAnimation called for walker index: {walkerIndex}");

        if (walkerIndex < 0 || walkerIndex >= spawnedAnimators.Count)
        {
            Debug.LogError($"❌ Invalid walker index {walkerIndex}! Valid range: 0-{spawnedAnimators.Count - 1}");
            return;
        }
        if (walkerIndex >= walkerDeathStates.Count)
        {
            Debug.LogError($"❌ Walker index {walkerIndex} out of death states range! Count: {walkerDeathStates.Count}");
            return;
        }

        // Don't trigger death if already dead
        if (walkerDeathStates[walkerIndex])
        {
            Debug.Log($"⚠️ Walker {walkerIndex} is already dead, skipping death animation");
            return;
        }

        GameObject walkerObject = spawnedPrefabs[walkerIndex];
        Animator animator = spawnedAnimators[walkerIndex];

        Debug.Log($"🎯 Processing death for Walker {walkerIndex} (GameObject: {walkerObject?.name})");

        if (animator == null)
        {
            Debug.LogError($"❌ No animator found for walker {walkerIndex}!");
            return;
        }

        // Mark as dead
        walkerDeathStates[walkerIndex] = true;

        // Stop walking and idle animations
        SetWalkingAnimation(walkerIndex, false);

        // Trigger death animation
        if (!string.IsNullOrEmpty(deathAnimationParameter))
        {
            animator.SetTrigger(deathAnimationParameter);
            Debug.Log($"💀 Walker {walkerIndex} ({walkerObject?.name}) DEATH ANIMATION TRIGGERED! Parameter: {deathAnimationParameter}");
        }
        else
        {
            Debug.LogError($"❌ Death animation parameter is empty!");
        }
    }

    /// <summary>
    /// Checks if a walker is dead
    /// </summary>
    private bool IsWalkerDead(int walkerIndex)
    {
        if (walkerIndex < 0 || walkerIndex >= walkerDeathStates.Count) return false;
        return walkerDeathStates[walkerIndex];
    }

    private void Update()
    {
        if (waypointsContainer.waypoints.Count == 0)
        {
            Debug.LogWarning("No waypoints found.");
            return;
        }

        // Get player position once per frame for efficiency
        Vector3 playerPosition = GetPlayerPosition();
        bool playerExists = IsPlayerVehicleActive();

        for (int i = 0; i < spawnedPrefabs.Count; i++)
        {
            GameObject spawnedPrefab = spawnedPrefabs[i];
            int currentWaypointIndex = currentWaypointIndices[i];
            int direction = waypointDirections[i];

            if (spawnedPrefab == null) continue;

            // Skip processing if walker is dead
            if (IsWalkerDead(i))
            {
                continue;
            }

            float distanceToPlayer = playerExists ? Vector3.Distance(playerPosition, spawnedPrefab.transform.position) : float.MaxValue;

            // Activate or deactivate prefab based on player range
            if (distanceToPlayer <= activationRange)
            {
                if (!spawnedPrefab.activeInHierarchy)
                {
                    spawnedPrefab.SetActive(true);
                }
            }
            else
            {
                if (spawnedPrefab.activeInHierarchy)
                {
                    spawnedPrefab.SetActive(false);
                    // Set idle animation when deactivated
                    SetWalkingAnimation(i, false);
                }
                continue; // Skip movement if the prefab is inactive
            }

            // Check if player is within stop radius (for idle behavior)
            bool playerNearby = distanceToPlayer <= playerStopRadius;

            if (playerNearby)
            {
                // Player is nearby - stop walking and play idle animation
                SetWalkingAnimation(i, false);
                continue; // Skip movement when player is nearby
            }

            // Movement logic for active prefabs - walker is moving
            SetWalkingAnimation(i, true);

            Transform currentWaypoint = waypointsContainer.waypoints[currentWaypointIndex].transform;
            Vector3 directionVector = currentWaypoint.position - spawnedPrefab.transform.position;

            Vector3 combinedMovement = directionVector.normalized;

            if (directionVector.magnitude < 0.1f)
            {
                if (currentWaypointIndex == 0 && direction == -1)
                {
                    direction = 1;
                }
                else if (currentWaypointIndex == waypointsContainer.waypoints.Count - 1 && direction == 1)
                {
                    direction = -1;

                    // Stop walking animation when reaching end
                    SetWalkingAnimation(i, false);

                    // Deactivate the prefab when it reaches the last waypoint
                    spawnedPrefab.SetActive(false);

                    // Reactivate the prefab at the first waypoint after a delay
                    StartCoroutine(ReactivateAtFirstWaypoint(spawnedPrefab, i));
                    continue; // Skip further processing for this prefab
                }
                currentWaypointIndex += direction;
            }

            spawnedPrefab.transform.Translate(combinedMovement * moveSpeed * Time.deltaTime, Space.World);
            Quaternion targetRotation = Quaternion.LookRotation(directionVector);
            spawnedPrefab.transform.rotation = Quaternion.Slerp(spawnedPrefab.transform.rotation, targetRotation, Time.deltaTime * moveSpeed);

            currentWaypointIndices[i] = currentWaypointIndex;
            waypointDirections[i] = direction;
        }
    }

    private IEnumerator ReactivateAtFirstWaypoint(GameObject prefab, int walkerIndex)
    {
        // Reactivate and move the prefab to the first waypoint
        int firstWaypointIndex = 0;
        Vector3 firstWaypointPosition = waypointsContainer.waypoints[firstWaypointIndex].transform.position;

        // Move the prefab to the first waypoint immediately
        prefab.transform.position = firstWaypointPosition;
        prefab.transform.rotation = Quaternion.identity; // Reset rotation or set it to a specific direction if needed
        prefab.SetActive(true);

        // Reset the prefab's state
        currentWaypointIndices[walkerIndex] = firstWaypointIndex;
        waypointDirections[walkerIndex] = 1; // Set direction to move forward from the first waypoint

        // Reset animation state to idle initially
        SetWalkingAnimation(walkerIndex, false);

        // Correct the prefab's rotation to face the next waypoint
        if (waypointsContainer.waypoints.Count > 1)
        {
            Transform nextWaypoint = waypointsContainer.waypoints[1].transform;
            Vector3 directionVector = nextWaypoint.position - prefab.transform.position;
            if (directionVector != Vector3.zero)
            {
                Quaternion targetRotation = Quaternion.LookRotation(directionVector);
                prefab.transform.rotation = targetRotation;
            }
        }

        yield return null; // Allow one frame for the prefab to be activated and start moving
    }


}
